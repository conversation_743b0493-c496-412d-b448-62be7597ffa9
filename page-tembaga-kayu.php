<?php
/**
 * Template Name: Tembaga Kayu Landing Page
 * Description: Premium landing page for Tembaga Kayu hand-forged copper sinks and bathtubs
 * Inspired by MACH.la minimalist design principles
 */

get_header(); ?>

<style>
/* Include the main styles inline for WordPress compatibility */
<?php include 'styles.css'; ?>
</style>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        
        <!-- Hero Section -->
        <section id="home" class="hero">
            <div class="hero-background">
                <?php
                $hero_image = get_template_directory_uri() . '/images/kerajinan-seni-kriya-logam-dukuh-tumang-desa-cepogo-kecamatan-cepogo-kabupaten-boyolali-1_169.jpeg';
                ?>
                <img src="<?php echo esc_url($hero_image); ?>" alt="<?php esc_attr_e('Artisan hand-forging copper', 'tembaga-kayu'); ?>" loading="eager">
                <div class="hero-overlay"></div>
            </div>
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        <?php _e('Hand-Forged Copper Artistry', 'tembaga-kayu'); ?>
                        <br><span class="hero-subtitle"><?php _e('Where Time-Honored Techniques Meet Contemporary Design', 'tembaga-kayu'); ?></span>
                    </h1>
                    <p class="hero-description">
                        <?php _e('Copper is not shaped, it is listened to. Every sink and bathtub begins as a dialogue between artisan and material, hammered into timeless form with patina deepening like a well-loved story', 'tembaga-kayu'); ?>
                    </p>
                    <div class="hero-cta">
                        <a href="#contact" class="btn-primary"><?php _e('Begin the Dialogue', 'tembaga-kayu'); ?></a>
                        <a href="#craft" class="btn-secondary"><?php _e('Explore Our Craft', 'tembaga-kayu'); ?></a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Product Showcase Carousel -->
        <section id="craft" class="product-showcase">
            <div class="container">
                <div class="section-header">
                    <h2><?php _e('Living Copper, Timeless Form', 'tembaga-kayu'); ?></h2>
                    <p><?php _e('Each piece tells a story of dialogue between artisan and material', 'tembaga-kayu'); ?></p>
                </div>
                
                <div class="product-gallery">
                    <div class="gallery-container">
                        <?php
                        // Define gallery items
                        $gallery_items = array(
                            array(
                                'image' => 'TK-WEBSITE-PICTURE-1.png',
                                'title' => __('Artisan Basin', 'tembaga-kayu'),
                                'description' => __('Premium copper basins crafted with precision<br>Each piece unique in character and finish', 'tembaga-kayu'),
                                'alt' => __('Hand-hammered copper basin with frosted blue oxidized patina', 'tembaga-kayu')
                            ),
                            array(
                                'image' => 'TK-WEBSITE-PICTURE-2.png',
                                'title' => __('Bespoke Bathtub', 'tembaga-kayu'),
                                'description' => __('Luxury copper bathtubs designed for your space<br>Where relaxation meets artistry', 'tembaga-kayu'),
                                'alt' => __('Hand-peened copper bathtub in raw copper finish', 'tembaga-kayu')
                            ),
                            array(
                                'image' => 'TK-WEBSITE-PICTURE-3.png',
                                'title' => __('Kitchen Vessel', 'tembaga-kayu'),
                                'description' => __('Functional beauty for culinary spaces<br>Copper sinks that enhance your kitchen', 'tembaga-kayu'),
                                'alt' => __('Contemporary copper kitchen sink', 'tembaga-kayu')
                            ),
                            array(
                                'image' => 'TK-website-1-1.jpg',
                                'title' => __('The Craft', 'tembaga-kayu'),
                                'description' => __('Traditional techniques passed through generations<br>Each piece tells a story of heritage', 'tembaga-kayu'),
                                'alt' => __('Artisan crafting process', 'tembaga-kayu')
                            ),
                            array(
                                'image' => 'TK-website-18.jpg',
                                'title' => __('Living Patina', 'tembaga-kayu'),
                                'description' => __('Designed to evolve with your home<br>Beauty that deepens with time', 'tembaga-kayu'),
                                'alt' => __('Copper texture detail', 'tembaga-kayu')
                            ),
                            array(
                                'image' => 'kerajinan-seni-kriya-logam-dukuh-tumang-desa-cepogo-kecamatan-cepogo-kabupaten-boyolali-1_169.jpeg',
                                'title' => __('Artisan Workshop', 'tembaga-kayu'),
                                'description' => __('Where tradition meets innovation<br>Skilled craftspeople creating timeless pieces', 'tembaga-kayu'),
                                'alt' => __('Traditional workshop setting', 'tembaga-kayu')
                            )
                        );
                        
                        foreach ($gallery_items as $index => $item) :
                            $active_class = $index === 0 ? 'active' : '';
                            $image_url = get_template_directory_uri() . '/images/' . $item['image'];
                        ?>
                        <div class="gallery-item <?php echo $active_class; ?>" data-product="<?php echo esc_attr(sanitize_title($item['title'])); ?>">
                            <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($item['alt']); ?>" loading="lazy">
                            <div class="gallery-caption">
                                <h3><?php echo esc_html($item['title']); ?></h3>
                                <p><?php echo wp_kses_post($item['description']); ?></p>
                                <a href="#contact" class="gallery-cta"><?php _e('Request Quote', 'tembaga-kayu'); ?></a>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="gallery-nav">
                        <button class="gallery-prev" aria-label="<?php esc_attr_e('Previous image', 'tembaga-kayu'); ?>">‹</button>
                        <button class="gallery-next" aria-label="<?php esc_attr_e('Next image', 'tembaga-kayu'); ?>">›</button>
                    </div>
                    
                    <div class="gallery-dots">
                        <?php for ($i = 0; $i < count($gallery_items); $i++) : ?>
                            <button class="dot <?php echo $i === 0 ? 'active' : ''; ?>" data-slide="<?php echo $i; ?>"></button>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>
        </section>

        <!-- Unique Selling Points Grid -->
        <section class="usp-grid">
            <div class="container">
                <div class="usp-cards">
                    <?php
                    $usp_items = array(
                        array(
                            'image' => 'TK-website-9.jpg',
                            'title' => __('Antimicrobial Properties', 'tembaga-kayu'),
                            'description' => __('Copper\'s living nature, naturally antimicrobial, enduring, and rich with evolving character', 'tembaga-kayu'),
                            'alt' => __('Antimicrobial copper properties', 'tembaga-kayu')
                        ),
                        array(
                            'image' => 'TK-website-10.jpg',
                            'title' => __('Living Patina', 'tembaga-kayu'),
                            'description' => __('Designed to evolve with your home, their patina deepening like a well-loved story', 'tembaga-kayu'),
                            'alt' => __('Living patina development', 'tembaga-kayu')
                        ),
                        array(
                            'image' => 'TK-website-12.jpg',
                            'title' => __('Artisan Craftsmanship', 'tembaga-kayu'),
                            'description' => __('Meticulously handcrafted by skilled local artisans, using techniques passed through generations', 'tembaga-kayu'),
                            'alt' => __('Artisan craftsmanship', 'tembaga-kayu')
                        )
                    );
                    
                    foreach ($usp_items as $item) :
                        $image_url = get_template_directory_uri() . '/images/' . $item['image'];
                    ?>
                    <div class="usp-card">
                        <div class="usp-image">
                            <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($item['alt']); ?>" loading="lazy">
                        </div>
                        <div class="usp-content">
                            <h3><?php echo esc_html($item['title']); ?></h3>
                            <p><?php echo esc_html($item['description']); ?></p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>

        <!-- About Section with Founder -->
        <section id="about" class="about">
            <div class="container">
                <div class="about-content">
                    <div class="about-text">
                        <h2><?php _e('The Philosophy Behind Every Piece', 'tembaga-kayu'); ?></h2>
                        <p class="about-philosophy">
                            <?php _e('At Tembaga Kayu, every sink and bathtub begins as a dialogue between artisan and material, between tradition and intention', 'tembaga-kayu'); ?>
                        </p>
                        
                        <div class="about-details">
                            <p><?php _e('We specialize in tailor-made designs that reflect your vision. From bold hammered bathtubs to minimalist vessel sinks, each piece begins with dialogue and ends as a testament to shared intention.', 'tembaga-kayu'); ?></p>
                            
                            <div class="social-impact">
                                <h4><?php _e('Luxury That Ripples Outward', 'tembaga-kayu'); ?></h4>
                                <p><?php _e('A portion of every Tembaga Kayu piece directly supports sanitation and education initiatives in our communities', 'tembaga-kayu'); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="about-founder">
                        <?php 
                        $founder_image = get_template_directory_uri() . '/images/about/Mir.jpg';
                        ?>
                        <img src="<?php echo esc_url($founder_image); ?>" alt="<?php esc_attr_e('Mirsa Ratina, Founder of Tembaga Kayu', 'tembaga-kayu'); ?>" loading="lazy">
                        <div class="founder-caption">
                            <h4><?php _e('Mirsa Ratina', 'tembaga-kayu'); ?></h4>
                            <p><?php _e('Founder, preserving traditional copper craftsmanship for future generations', 'tembaga-kayu'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Bespoke Process -->
        <section class="process">
            <div class="container">
                <div class="section-header">
                    <h2><?php _e('The Bespoke Journey', 'tembaga-kayu'); ?></h2>
                    <p><?php _e('From dialogue to delivery, every step crafted with intention', 'tembaga-kayu'); ?></p>
                </div>
                
                <div class="process-steps">
                    <?php
                    $process_steps = array(
                        array(
                            'number' => '01',
                            'title' => __('Consultation', 'tembaga-kayu'),
                            'description' => __('We begin with dialogue, understanding your vision and space requirements', 'tembaga-kayu')
                        ),
                        array(
                            'number' => '02',
                            'title' => __('Design Draft', 'tembaga-kayu'),
                            'description' => __('Detailed specifications and visual concepts tailored to your needs', 'tembaga-kayu')
                        ),
                        array(
                            'number' => '03',
                            'title' => __('Hand-Forging', 'tembaga-kayu'),
                            'description' => __('Master artisans bring your vision to life using traditional techniques', 'tembaga-kayu')
                        ),
                        array(
                            'number' => '04',
                            'title' => __('Delivery', 'tembaga-kayu'),
                            'description' => __('Careful packaging and shipping to preserve the integrity of your piece', 'tembaga-kayu')
                        )
                    );
                    
                    foreach ($process_steps as $step) :
                    ?>
                    <div class="process-step">
                        <div class="step-number"><?php echo esc_html($step['number']); ?></div>
                        <h3><?php echo esc_html($step['title']); ?></h3>
                        <p><?php echo esc_html($step['description']); ?></p>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="process-cta">
                    <a href="#contact" class="btn-primary"><?php _e('Begin Your Journey', 'tembaga-kayu'); ?></a>
                </div>
            </div>
        </section>

        <!-- Pricing and Trade Information -->
        <section class="pricing">
            <div class="container">
                <div class="section-header">
                    <h2><?php _e('Investment & Timelines', 'tembaga-kayu'); ?></h2>
                    <p><?php _e('Transparent pricing for exceptional craftsmanship', 'tembaga-kayu'); ?></p>
                </div>
                
                <div class="pricing-grid">
                    <?php
                    $pricing_tiers = array(
                        array(
                            'title' => __('Bespoke Basins & Smaller Sinks', 'tembaga-kayu'),
                            'price' => __('From €250', 'tembaga-kayu'),
                            'timeline' => __('1-3 weeks production', 'tembaga-kayu'),
                            'features' => array(
                                __('Custom dimensions', 'tembaga-kayu'),
                                __('Choice of patina finish', 'tembaga-kayu'),
                                __('Hand-hammered texture', 'tembaga-kayu')
                            ),
                            'featured' => false
                        ),
                        array(
                            'title' => __('Bespoke Kitchen Sinks', 'tembaga-kayu'),
                            'price' => __('From €500', 'tembaga-kayu'),
                            'timeline' => __('4-8 weeks production', 'tembaga-kayu'),
                            'features' => array(
                                __('Tailored to your kitchen', 'tembaga-kayu'),
                                __('Multiple basin options', 'tembaga-kayu'),
                                __('Integrated accessories', 'tembaga-kayu')
                            ),
                            'featured' => true
                        ),
                        array(
                            'title' => __('Bespoke Bathtubs', 'tembaga-kayu'),
                            'price' => __('From €2,650', 'tembaga-kayu'),
                            'timeline' => __('8-12 weeks production', 'tembaga-kayu'),
                            'features' => array(
                                __('Fully customizable design', 'tembaga-kayu'),
                                __('Premium copper thickness', 'tembaga-kayu'),
                                __('Unique artistic elements', 'tembaga-kayu')
                            ),
                            'featured' => false
                        )
                    );
                    
                    foreach ($pricing_tiers as $tier) :
                        $featured_class = $tier['featured'] ? 'featured' : '';
                    ?>
                    <div class="pricing-card <?php echo $featured_class; ?>">
                        <h3><?php echo esc_html($tier['title']); ?></h3>
                        <div class="price"><?php echo esc_html($tier['price']); ?></div>
                        <div class="timeline"><?php echo esc_html($tier['timeline']); ?></div>
                        <ul>
                            <?php foreach ($tier['features'] as $feature) : ?>
                                <li><?php echo esc_html($feature); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="trade-info">
                    <h3><?php _e('Trade & Professional Discounts', 'tembaga-kayu'); ?></h3>
                    <p><?php _e('Special pricing available for trade professionals and volume orders. Contact us to discuss your specific requirements and partnership opportunities.', 'tembaga-kayu'); ?></p>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="contact">
            <div class="container">
                <div class="contact-content">
                    <div class="contact-info">
                        <h2><?php _e('Begin the Dialogue', 'tembaga-kayu'); ?></h2>
                        <p><?php _e('Every masterpiece starts with a conversation', 'tembaga-kayu'); ?></p>
                        
                        <div class="contact-details">
                            <div class="contact-item">
                                <h4><?php _e('Phone', 'tembaga-kayu'); ?></h4>
                                <p>+62 815 42056768<br>+34 613 145 100</p>
                            </div>
                            
                            <div class="contact-item">
                                <h4><?php _e('Email', 'tembaga-kayu'); ?></h4>
                                <p><EMAIL></p>
                            </div>
                            
                            <div class="contact-item">
                                <h4><?php _e('Website', 'tembaga-kayu'); ?></h4>
                                <p>www.tembagakayu.com</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-form">
                        <?php
                        // WordPress Contact Form 7 shortcode or custom form
                        if (function_exists('wpcf7_enqueue_scripts')) {
                            echo do_shortcode('[contact-form-7 id="tembaga-kayu-consultation" title="Consultation Request"]');
                        } else {
                            // Fallback custom form
                            include 'contact-form-fallback.php';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </section>

    </main>
</div>

<script>
/* Include the main JavaScript inline for WordPress compatibility */
<?php include 'script.js'; ?>
</script>

<?php get_footer(); ?>
