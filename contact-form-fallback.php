<?php
/**
 * Fallback Contact Form for Tembaga Kayu
 * Used when Contact Form 7 is not available
 */

// Handle form submission
if ($_POST && isset($_POST['tembaga_kayu_nonce']) && wp_verify_nonce($_POST['tembaga_kayu_nonce'], 'tembaga_kayu_contact')) {
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $project_type = sanitize_text_field($_POST['project-type']);
    $message = sanitize_textarea_field($_POST['message']);
    
    // Validate required fields
    $errors = array();
    if (empty($name)) $errors[] = __('Name is required', 'tembaga-kayu');
    if (empty($email) || !is_email($email)) $errors[] = __('Valid email is required', 'tembaga-kayu');
    if (empty($project_type)) $errors[] = __('Project type is required', 'tembaga-kayu');
    if (empty($message)) $errors[] = __('Message is required', 'tembaga-kayu');
    
    if (empty($errors)) {
        // Prepare email
        $to = '<EMAIL>';
        $subject = sprintf(__('New Consultation Request from %s', 'tembaga-kayu'), $name);
        
        $email_body = sprintf(
            __("New consultation request from Tembaga Kayu website:\n\nName: %s\nEmail: %s\nPhone: %s\nProject Type: %s\n\nMessage:\n%s\n\nSubmitted: %s", 'tembaga-kayu'),
            $name,
            $email,
            $phone,
            $project_type,
            $message,
            current_time('mysql')
        );
        
        $headers = array(
            'Content-Type: text/plain; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <noreply@' . $_SERVER['HTTP_HOST'] . '>',
            'Reply-To: ' . $name . ' <' . $email . '>'
        );
        
        // Send email
        $sent = wp_mail($to, $subject, $email_body, $headers);
        
        if ($sent) {
            $success_message = __('Thank you! We\'ll be in touch within 24 hours to begin the dialogue.', 'tembaga-kayu');
            
            // Clear form data
            $name = $email = $phone = $project_type = $message = '';
        } else {
            $errors[] = __('Sorry, there was an error sending your message. Please try again or contact us directly.', 'tembaga-kayu');
        }
    }
}
?>

<?php if (isset($success_message)) : ?>
    <div class="form-message success">
        <?php echo esc_html($success_message); ?>
    </div>
<?php endif; ?>

<?php if (!empty($errors)) : ?>
    <div class="form-message error">
        <?php foreach ($errors as $error) : ?>
            <p><?php echo esc_html($error); ?></p>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<form id="consultation-form" method="post" action="">
    <?php wp_nonce_field('tembaga_kayu_contact', 'tembaga_kayu_nonce'); ?>
    
    <div class="form-group">
        <label for="name"><?php _e('Name', 'tembaga-kayu'); ?> *</label>
        <input type="text" id="name" name="name" value="<?php echo isset($name) ? esc_attr($name) : ''; ?>" required>
    </div>
    
    <div class="form-group">
        <label for="email"><?php _e('Email', 'tembaga-kayu'); ?> *</label>
        <input type="email" id="email" name="email" value="<?php echo isset($email) ? esc_attr($email) : ''; ?>" required>
    </div>
    
    <div class="form-group">
        <label for="phone"><?php _e('Phone', 'tembaga-kayu'); ?></label>
        <input type="tel" id="phone" name="phone" value="<?php echo isset($phone) ? esc_attr($phone) : ''; ?>">
    </div>
    
    <div class="form-group">
        <label for="project-type"><?php _e('Project Type', 'tembaga-kayu'); ?> *</label>
        <select id="project-type" name="project-type" required>
            <option value=""><?php _e('Select a project type', 'tembaga-kayu'); ?></option>
            <option value="basin" <?php selected(isset($project_type) ? $project_type : '', 'basin'); ?>><?php _e('Basin/Smaller Sink', 'tembaga-kayu'); ?></option>
            <option value="kitchen-sink" <?php selected(isset($project_type) ? $project_type : '', 'kitchen-sink'); ?>><?php _e('Kitchen Sink', 'tembaga-kayu'); ?></option>
            <option value="bathtub" <?php selected(isset($project_type) ? $project_type : '', 'bathtub'); ?>><?php _e('Bathtub', 'tembaga-kayu'); ?></option>
            <option value="custom" <?php selected(isset($project_type) ? $project_type : '', 'custom'); ?>><?php _e('Custom Project', 'tembaga-kayu'); ?></option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="message"><?php _e('Tell us about your vision', 'tembaga-kayu'); ?> *</label>
        <textarea id="message" name="message" rows="5" placeholder="<?php esc_attr_e('Describe your project, dimensions, style preferences, and any specific requirements...', 'tembaga-kayu'); ?>" required><?php echo isset($message) ? esc_textarea($message) : ''; ?></textarea>
    </div>
    
    <button type="submit" class="btn-primary"><?php _e('Send Consultation Request', 'tembaga-kayu'); ?></button>
</form>
