<?php
/**
 * Tembaga Kayu WordPress Theme Functions
 * Add this to your theme's functions.php file
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue Tembaga Kayu styles and scripts
 */
function tembaga_kayu_enqueue_assets() {
    // Enqueue Google Fonts
    wp_enqueue_style(
        'tembaga-kayu-fonts',
        'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600&display=swap',
        array(),
        '1.0.0'
    );
    
    // Enqueue main stylesheet
    wp_enqueue_style(
        'tembaga-kayu-styles',
        get_template_directory_uri() . '/styles.css',
        array('tembaga-kayu-fonts'),
        '1.0.0'
    );
    
    // Enqueue main JavaScript
    wp_enqueue_script(
        'tembaga-kayu-script',
        get_template_directory_uri() . '/script.js',
        array('jquery'),
        '1.0.0',
        true
    );
    
    // Localize script for AJAX
    wp_localize_script('tembaga-kayu-script', 'tembaga_kayu_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('tembaga_kayu_nonce'),
        'strings' => array(
            'sending' => __('Sending...', 'tembaga-kayu'),
            'success' => __('Thank you! We\'ll be in touch within 24 hours to begin the dialogue.', 'tembaga-kayu'),
            'error' => __('Sorry, there was an error sending your message. Please try again or contact us directly.', 'tembaga-kayu')
        )
    ));
}
add_action('wp_enqueue_scripts', 'tembaga_kayu_enqueue_assets');

/**
 * Add theme support for various WordPress features
 */
function tembaga_kayu_theme_support() {
    // Add theme support for post thumbnails
    add_theme_support('post-thumbnails');
    
    // Add theme support for custom logo
    add_theme_support('custom-logo', array(
        'height'      => 100,
        'width'       => 400,
        'flex-height' => true,
        'flex-width'  => true,
    ));
    
    // Add theme support for HTML5 markup
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Add theme support for title tag
    add_theme_support('title-tag');
    
    // Add theme support for custom header
    add_theme_support('custom-header', array(
        'default-image' => get_template_directory_uri() . '/images/TK-WEBSITE-PICTURE-1.png',
        'width'         => 1920,
        'height'        => 1080,
        'flex-height'   => true,
        'flex-width'    => true,
    ));
}
add_action('after_setup_theme', 'tembaga_kayu_theme_support');

/**
 * Register navigation menus
 */
function tembaga_kayu_register_menus() {
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'tembaga-kayu'),
        'footer'  => __('Footer Menu', 'tembaga-kayu'),
    ));
}
add_action('init', 'tembaga_kayu_register_menus');

/**
 * Register widget areas
 */
function tembaga_kayu_widgets_init() {
    register_sidebar(array(
        'name'          => __('Footer Widget Area', 'tembaga-kayu'),
        'id'            => 'footer-widgets',
        'description'   => __('Add widgets here to appear in your footer.', 'tembaga-kayu'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'tembaga_kayu_widgets_init');

/**
 * Handle AJAX form submission
 */
function tembaga_kayu_handle_contact_form() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'tembaga_kayu_nonce')) {
        wp_die(__('Security check failed', 'tembaga-kayu'));
    }
    
    // Sanitize form data
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $project_type = sanitize_text_field($_POST['project_type']);
    $message = sanitize_textarea_field($_POST['message']);
    
    // Validate required fields
    $errors = array();
    if (empty($name)) $errors[] = __('Name is required', 'tembaga-kayu');
    if (empty($email) || !is_email($email)) $errors[] = __('Valid email is required', 'tembaga-kayu');
    if (empty($project_type)) $errors[] = __('Project type is required', 'tembaga-kayu');
    if (empty($message)) $errors[] = __('Message is required', 'tembaga-kayu');
    
    if (!empty($errors)) {
        wp_send_json_error(array('message' => implode('<br>', $errors)));
    }
    
    // Prepare email
    $to = get_option('admin_email'); // or '<EMAIL>'
    $subject = sprintf(__('New Consultation Request from %s', 'tembaga-kayu'), $name);
    
    $email_body = sprintf(
        __("New consultation request from Tembaga Kayu website:\n\nName: %s\nEmail: %s\nPhone: %s\nProject Type: %s\n\nMessage:\n%s\n\nSubmitted: %s\nIP Address: %s\nUser Agent: %s", 'tembaga-kayu'),
        $name,
        $email,
        $phone,
        $project_type,
        $message,
        current_time('mysql'),
        $_SERVER['REMOTE_ADDR'],
        $_SERVER['HTTP_USER_AGENT']
    );
    
    $headers = array(
        'Content-Type: text/plain; charset=UTF-8',
        'From: ' . get_bloginfo('name') . ' <noreply@' . $_SERVER['HTTP_HOST'] . '>',
        'Reply-To: ' . $name . ' <' . $email . '>'
    );
    
    // Send email
    $sent = wp_mail($to, $subject, $email_body, $headers);
    
    if ($sent) {
        // Save to database for backup
        $contact_data = array(
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'project_type' => $project_type,
            'message' => $message,
            'submitted_at' => current_time('mysql'),
            'ip_address' => $_SERVER['REMOTE_ADDR']
        );
        
        // You could save this to a custom table or as post meta
        // For now, we'll just send the success response
        
        wp_send_json_success(array(
            'message' => __('Thank you! We\'ll be in touch within 24 hours to begin the dialogue.', 'tembaga-kayu')
        ));
    } else {
        wp_send_json_error(array(
            'message' => __('Sorry, there was an error sending your message. Please try again or contact us directly.', 'tembaga-kayu')
        ));
    }
}
add_action('wp_ajax_tembaga_kayu_contact', 'tembaga_kayu_handle_contact_form');
add_action('wp_ajax_nopriv_tembaga_kayu_contact', 'tembaga_kayu_handle_contact_form');

/**
 * Add custom image sizes for Tembaga Kayu
 */
function tembaga_kayu_custom_image_sizes() {
    add_image_size('gallery-large', 800, 600, true);
    add_image_size('gallery-thumb', 150, 150, true);
    add_image_size('usp-card', 400, 300, true);
    add_image_size('hero-bg', 1920, 1080, true);
}
add_action('after_setup_theme', 'tembaga_kayu_custom_image_sizes');

/**
 * Optimize images for performance
 */
function tembaga_kayu_optimize_images($attr, $attachment, $size) {
    // Add loading="lazy" to images (WordPress 5.5+ does this automatically)
    if (!isset($attr['loading'])) {
        $attr['loading'] = 'lazy';
    }
    
    // Add decoding="async" for better performance
    $attr['decoding'] = 'async';
    
    return $attr;
}
add_filter('wp_get_attachment_image_attributes', 'tembaga_kayu_optimize_images', 10, 3);

/**
 * Add structured data for SEO
 */
function tembaga_kayu_structured_data() {
    if (is_page_template('page-tembaga-kayu.php')) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'Tembaga Kayu',
            'url' => home_url(),
            'logo' => get_template_directory_uri() . '/images/TK-WEBSITE-PICTURE-1.png',
            'description' => 'Hand-forged copper sinks and bathtubs crafted by skilled artisans using traditional techniques',
            'contactPoint' => array(
                '@type' => 'ContactPoint',
                'telephone' => '+62 815 42056768',
                'contactType' => 'customer service',
                'email' => '<EMAIL>'
            ),
            'address' => array(
                '@type' => 'PostalAddress',
                'addressCountry' => 'ID'
            ),
            'sameAs' => array(
                'https://www.instagram.com/tembagakayu',
                'https://www.facebook.com/tembagakayu'
            )
        );
        
        echo '<script type="application/ld+json">' . json_encode($schema) . '</script>';
    }
}
add_action('wp_head', 'tembaga_kayu_structured_data');

/**
 * Add meta tags for social sharing
 */
function tembaga_kayu_social_meta() {
    if (is_page_template('page-tembaga-kayu.php')) {
        echo '<meta property="og:title" content="Tembaga Kayu - Hand-Forged Copper Artistry">';
        echo '<meta property="og:description" content="Copper is not shaped, it is listened to. Every sink and bathtub begins as a dialogue between artisan and material.">';
        echo '<meta property="og:image" content="' . get_template_directory_uri() . '/images/TK-WEBSITE-PICTURE-1.png">';
        echo '<meta property="og:url" content="' . get_permalink() . '">';
        echo '<meta property="og:type" content="website">';
        echo '<meta name="twitter:card" content="summary_large_image">';
        echo '<meta name="twitter:title" content="Tembaga Kayu - Hand-Forged Copper Artistry">';
        echo '<meta name="twitter:description" content="Copper is not shaped, it is listened to. Every sink and bathtub begins as a dialogue between artisan and material.">';
        echo '<meta name="twitter:image" content="' . get_template_directory_uri() . '/images/TK-WEBSITE-PICTURE-1.png">';
    }
}
add_action('wp_head', 'tembaga_kayu_social_meta');

/**
 * Load text domain for translations
 */
function tembaga_kayu_load_textdomain() {
    load_theme_textdomain('tembaga-kayu', get_template_directory() . '/languages');
}
add_action('after_setup_theme', 'tembaga_kayu_load_textdomain');

/**
 * Customize WordPress admin for Tembaga Kayu
 */
function tembaga_kayu_admin_customization() {
    // Add custom CSS to admin
    echo '<style>
        .admin-color-scheme {
            --wp-admin-theme-color: #B87333;
            --wp-admin-theme-color-darker-10: #8B4513;
            --wp-admin-theme-color-darker-20: #654321;
        }
    </style>';
}
add_action('admin_head', 'tembaga_kayu_admin_customization');

/**
 * Add custom post type for testimonials (optional)
 */
function tembaga_kayu_register_testimonials() {
    $args = array(
        'public' => true,
        'label'  => __('Testimonials', 'tembaga-kayu'),
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-format-quote',
    );
    register_post_type('testimonial', $args);
}
add_action('init', 'tembaga_kayu_register_testimonials');

/**
 * Security enhancements
 */
function tembaga_kayu_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }
}
add_action('send_headers', 'tembaga_kayu_security_headers');

/**
 * Performance optimizations
 */
function tembaga_kayu_performance_optimizations() {
    // Remove unnecessary WordPress features for this landing page
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // Disable emojis if not needed
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
}
add_action('init', 'tembaga_kayu_performance_optimizations');
?>
