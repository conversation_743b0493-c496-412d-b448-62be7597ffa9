/* MACH.la-Inspired Minimalist Design for Tembaga Kayu */

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette - Inspired by copper and minimalist design */
    --copper-primary: #B87333;
    --copper-dark: #8B4513;
    --copper-light: #CD853F;
    --copper-accent: #D2691E;
    --white: #FFFFFF;
    --off-white: #FAFAFA;
    --light-gray: #F5F5F5;
    --medium-gray: #E0E0E0;
    --dark-gray: #333333;
    --text-primary: #1A1A1A;
    --text-secondary: #666666;
    --text-light: #999999;
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-display: 'Playfair Display', Georgia, serif;
    
    /* Spacing - MACH.la inspired generous spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 2rem;
    --spacing-lg: 4rem;
    --spacing-xl: 6rem;
    --spacing-xxl: 8rem;
    
    /* Layout */
    --container-max: 1200px;
    --container-padding: 2rem;
    --border-radius: 0.25rem;
    --transition: all 0.3s ease;
}

/* Base Typography */
html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-weight: 400;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    max-width: var(--container-max);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

/* Navigation - MACH.la inspired minimal nav */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 1px solid var(--medium-gray);
}

.nav-container {
    max-width: var(--container-max);
    margin: 0 auto;
    padding: 0 var(--container-padding);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.nav-logo a {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    text-decoration: none;
    letter-spacing: 0.05em;
}

.nav-links {
    display: flex;
    gap: var(--spacing-lg);
}

.nav-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 400;
    font-size: 0.95rem;
    transition: var(--transition);
    letter-spacing: 0.02em;
}

.nav-links a:hover {
    color: var(--copper-primary);
}

.nav-mobile-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-mobile-toggle span {
    width: 25px;
    height: 2px;
    background: var(--text-primary);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
    z-index: -1;
}

.hero-content {
    position: relative;
    z-index: 1;
    width: 100%;
    padding: 0 var(--container-padding);
    max-width: var(--container-max);
    margin: 0 auto;
}

.hero-text {
    max-width: 800px;
    color: var(--white);
}

.hero-title {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    letter-spacing: -0.02em;
}

.hero-subtitle {
    display: block;
    font-size: clamp(1.5rem, 3vw, 2.5rem);
    font-weight: 400;
    color: var(--copper-light);
    margin-top: var(--spacing-sm);
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
    max-width: 600px;
}

.hero-cta {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* Buttons */
.btn-primary {
    display: inline-block;
    background: var(--copper-primary);
    color: var(--white);
    padding: 1rem 2rem;
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: 2px solid var(--copper-primary);
    letter-spacing: 0.02em;
}

.btn-primary:hover {
    background: var(--copper-dark);
    border-color: var(--copper-dark);
    transform: translateY(-2px);
}

.btn-secondary {
    display: inline-block;
    background: transparent;
    color: var(--white);
    padding: 1rem 2rem;
    text-decoration: none;
    font-weight: 400;
    font-size: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    transition: var(--transition);
    letter-spacing: 0.02em;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.section-header h2 {
    font-family: var(--font-display);
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    letter-spacing: -0.01em;
}

.section-header p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Product Showcase */
.product-showcase {
    padding: var(--spacing-xxl) 0;
    background: var(--off-white);
}

.product-gallery {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
}

.gallery-container {
    position: relative;
    height: 600px;
    overflow: hidden;
    border-radius: var(--border-radius);
    background: var(--white);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.gallery-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.6s ease;
    display: flex;
}

.gallery-item.active {
    opacity: 1;
}

.gallery-item img {
    width: 60%;
    height: 100%;
    object-fit: cover;
}

.gallery-caption {
    width: 40%;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: var(--white);
}

.gallery-caption h3 {
    font-family: var(--font-display);
    font-size: 1.75rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.gallery-caption p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.gallery-cta {
    color: var(--copper-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    letter-spacing: 0.02em;
    transition: var(--transition);
    align-self: flex-start;
}

.gallery-cta:hover {
    color: var(--copper-dark);
}

.gallery-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 var(--spacing-md);
    pointer-events: none;
}

.gallery-prev,
.gallery-next {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.5rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    pointer-events: all;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-prev:hover,
.gallery-next:hover {
    background: var(--white);
    transform: scale(1.1);
}

.gallery-dots {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: var(--medium-gray);
    cursor: pointer;
    transition: var(--transition);
}

.dot.active {
    background: var(--copper-primary);
}

/* USP Grid */
.usp-grid {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.usp-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.usp-card {
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: var(--transition);
}

.usp-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.usp-image {
    height: 250px;
    overflow: hidden;
}

.usp-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.usp-card:hover .usp-image img {
    transform: scale(1.05);
}

.usp-content {
    padding: var(--spacing-lg);
}

.usp-content h3 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.usp-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* About Section */
.about {
    padding: var(--spacing-xxl) 0;
    background: var(--light-gray);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.about-text h2 {
    font-family: var(--font-display);
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    letter-spacing: -0.01em;
}

.about-philosophy {
    font-size: 1.25rem;
    color: var(--copper-primary);
    font-style: italic;
    margin-bottom: var(--spacing-lg);
    line-height: 1.5;
}

.about-details p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.social-impact {
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--copper-primary);
    margin-top: var(--spacing-lg);
}

.social-impact h4 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.social-impact p {
    color: var(--text-secondary);
    margin: 0;
}

.about-founder {
    text-align: center;
}

.about-founder img {
    width: 100%;
    max-width: 400px;
    height: 500px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.founder-caption {
    margin-top: var(--spacing-md);
}

.founder-caption h4 {
    font-family: var(--font-display);
    font-size: 1.25rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.founder-caption p {
    color: var(--text-secondary);
    font-style: italic;
}

/* Process Section */
.process {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.process-step {
    text-align: center;
    padding: var(--spacing-lg);
}

.step-number {
    display: inline-block;
    width: 60px;
    height: 60px;
    background: var(--copper-primary);
    color: var(--white);
    border-radius: 50%;
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 60px;
    margin-bottom: var(--spacing-md);
}

.process-step h3 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.process-step p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.process-cta {
    text-align: center;
    margin-top: var(--spacing-xl);
}

.lead-magnet {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--light-gray);
    border-radius: var(--border-radius);
    display: inline-block;
}

.lead-magnet p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.95rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --container-padding: 1rem;
        --spacing-lg: 2rem;
        --spacing-xl: 3rem;
        --spacing-xxl: 4rem;
    }
    
    .nav-links {
        display: none;
    }
    
    .nav-mobile-toggle {
        display: flex;
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .gallery-item {
        flex-direction: column;
    }
    
    .gallery-item img {
        width: 100%;
        height: 60%;
    }
    
    .gallery-caption {
        width: 100%;
        height: 40%;
        padding: var(--spacing-md);
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .about-founder {
        order: -1;
    }
    
    .process-steps {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

/* Pricing Section */
.pricing {
    padding: var(--spacing-xxl) 0;
    background: var(--light-gray);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.pricing-card {
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    text-align: center;
    transition: var(--transition);
    position: relative;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.pricing-card.featured {
    border: 2px solid var(--copper-primary);
    transform: scale(1.05);
}

.pricing-card.featured::before {
    content: "Most Popular";
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--copper-primary);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.85rem;
    font-weight: 500;
}

.pricing-card h3 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--copper-primary);
    margin-bottom: var(--spacing-sm);
}

.timeline {
    color: var(--text-secondary);
    font-size: 0.95rem;
    margin-bottom: var(--spacing-md);
}

.pricing-card ul {
    list-style: none;
    text-align: left;
}

.pricing-card li {
    padding: 0.5rem 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: 1.5rem;
}

.pricing-card li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--copper-primary);
    font-weight: bold;
}

.trade-info {
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.trade-info h3 {
    font-family: var(--font-display);
    font-size: 1.75rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.trade-info p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.payment-terms {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--medium-gray);
}

.payment-terms h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

/* Contact Section */
.contact {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.contact-info h2 {
    font-family: var(--font-display);
    font-size: clamp(2rem, 4vw, 3rem);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.contact-info p {
    color: var(--text-secondary);
    font-size: 1.125rem;
    margin-bottom: var(--spacing-lg);
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.contact-item h4 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.contact-item p {
    color: var(--text-secondary);
    margin: 0;
}

.contact-form {
    background: var(--light-gray);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    font-family: var(--font-primary);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--copper-primary);
    box-shadow: 0 0 0 3px rgba(184, 115, 51, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Form Messages */
.form-message {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
    font-weight: 500;
}

.form-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.form-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Mobile Navigation Styles */
@media (max-width: 768px) {
    .nav-links {
        position: fixed;
        top: 80px;
        left: 0;
        right: 0;
        background: var(--white);
        flex-direction: column;
        padding: var(--spacing-lg);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
    }

    .nav-links.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-links a {
        padding: var(--spacing-sm) 0;
        font-size: 1.1rem;
        border-bottom: 1px solid var(--medium-gray);
    }

    .nav-links a:last-child {
        border-bottom: none;
    }

    .nav-mobile-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-mobile-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-mobile-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

/* Footer */
.footer {
    background: var(--dark-gray);
    color: var(--white);
    padding: var(--spacing-xl) 0 var(--spacing-md);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.footer-brand h3 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--copper-light);
}

.footer-brand p {
    color: var(--text-light);
    line-height: 1.6;
}

.footer-links {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.footer-column h4 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.footer-column ul {
    list-style: none;
}

.footer-column li {
    margin-bottom: var(--spacing-xs);
}

.footer-column a {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
}

.footer-column a:hover {
    color: var(--copper-light);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid #555;
}

.footer-bottom p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin: 0;
}

/* Responsive Design Continued */
@media (max-width: 768px) {
    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .pricing-card.featured {
        transform: none;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .footer-links {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero-description {
        font-size: 1.1rem;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        text-align: center;
    }

    .usp-cards {
        grid-template-columns: 1fr;
    }

    .gallery-container {
        height: 500px;
    }

    .pricing-card {
        padding: var(--spacing-md);
    }

    .contact-form {
        padding: var(--spacing-md);
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-copper {
    color: var(--copper-primary);
}

.bg-copper {
    background-color: var(--copper-primary);
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Focus Styles for Accessibility */
.btn-primary:focus,
.btn-secondary:focus,
.gallery-prev:focus,
.gallery-next:focus,
.dot:focus {
    outline: 2px solid var(--copper-primary);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .nav,
    .hero,
    .footer {
        display: none;
    }

    .container {
        max-width: none;
        padding: 0;
    }

    * {
        background: white !important;
        color: black !important;
    }
}
